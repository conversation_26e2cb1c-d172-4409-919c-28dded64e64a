"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "./button"
import { Card } from "./card"
import { Badge } from "./badge"
import { ChatMessageList, ChatMessage } from "./chat-message"
import { ChatInput } from "./chat-input"
import {
  MessageCircle,
  X,
  Minimize2,
  Maximize2,
  Bot,
  Sparkles,
  WifiOff,
  AlertTriangle
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { InlineError, LoadingState, useNetworkStatus } from "./chat-error-boundary"

interface ChatWidgetProps {
  className?: string
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left"
  title?: string
  subtitle?: string
  placeholder?: string
  onSendMessage?: (message: string) => Promise<string>
  initialMessages?: ChatMessage[]
  disabled?: boolean
}

export function ChatWidget({
  className,
  position = "bottom-right",
  title = "AI Assistant",
  subtitle = "Ask me anything!",
  placeholder = "Type your message...",
  onSendMessage,
  initialMessages = [],
  disabled = false,
}: ChatWidgetProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [isMinimized, setIsMinimized] = React.useState(false)
  const [messages, setMessages] = React.useState<ChatMessage[]>(initialMessages)
  const [isLoading, setIsLoading] = React.useState(false)
  const [unreadCount, setUnreadCount] = React.useState(0)
  const [error, setError] = React.useState<string | null>(null)
  const [retryCount, setRetryCount] = React.useState(0)
  const isOnline = useNetworkStatus()

  const positionClasses = {
    "bottom-right": "bottom-4 right-4",
    "bottom-left": "bottom-4 left-4", 
    "top-right": "top-4 right-4",
    "top-left": "top-4 left-4",
  }

  const handleSendMessage = async (content: string) => {
    if (!onSendMessage) return

    // Check network connectivity
    if (!isOnline) {
      setError("No internet connection. Please check your network and try again.")
      return
    }

    // Clear any previous errors
    setError(null)

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content,
      role: "user",
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)

    // Add loading message
    const loadingMessage: ChatMessage = {
      id: `loading-${Date.now()}`,
      content: "",
      role: "assistant",
      timestamp: new Date(),
      isLoading: true,
    }

    setMessages(prev => [...prev, loadingMessage])

    try {
      const response = await onSendMessage(content)

      // Remove loading message and add actual response
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isLoading)
        const assistantMessage: ChatMessage = {
          id: Date.now().toString(),
          content: response,
          role: "assistant",
          timestamp: new Date(),
        }
        return [...filtered, assistantMessage]
      })

      // Reset retry count on success
      setRetryCount(0)

      // Increment unread count if chat is closed
      if (!isOpen) {
        setUnreadCount(prev => prev + 1)
      }
    } catch (error) {
      console.error("Chat error:", error)

      // Determine error message based on error type
      let errorMessage = "Sorry, I encountered an error. Please try again."

      if (error instanceof Error) {
        if (error.message.includes("rate limit")) {
          errorMessage = "Too many requests. Please wait a moment before trying again."
        } else if (error.message.includes("network") || error.message.includes("fetch")) {
          errorMessage = "Network error. Please check your connection and try again."
        } else if (error.message.includes("401") || error.message.includes("unauthorized")) {
          errorMessage = "Authentication error. Please contact support."
        } else if (error.message.includes("500")) {
          errorMessage = "Server error. Please try again in a few moments."
        }
      }

      // Remove loading message and add error message
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isLoading)
        const errorChatMessage: ChatMessage = {
          id: Date.now().toString(),
          content: errorMessage,
          role: "assistant",
          timestamp: new Date(),
        }
        return [...filtered, errorChatMessage]
      })

      // Set error state for UI feedback
      setError(errorMessage)
      setRetryCount(prev => prev + 1)
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleChat = () => {
    setIsOpen(!isOpen)
    if (!isOpen) {
      setUnreadCount(0) // Reset unread count when opening
    }
  }

  const handleMinimize = () => {
    setIsMinimized(!isMinimized)
  }

  const handleClose = () => {
    setIsOpen(false)
    setIsMinimized(false)
  }

  return (
    <div className={cn("fixed z-50", positionClasses[position], className)}>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "mb-4 w-80 sm:w-96",
              position.includes("bottom") ? "mb-4" : "mt-4"
            )}
          >
            <Card className={cn(
              "flex flex-col shadow-lg border-0 bg-background/95 backdrop-blur-sm",
              isMinimized ? "h-14" : "h-[500px]"
            )}>
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b bg-primary text-primary-foreground rounded-t-xl">
                <div className="flex items-center gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-foreground/20 relative">
                    <Bot className="h-4 w-4" />
                    {!isOnline && (
                      <div className="absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full flex items-center justify-center">
                        <WifiOff className="h-2 w-2 text-white" />
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col">
                    <div className="flex items-center gap-2">
                      <h3 className="text-sm font-semibold">{title}</h3>
                      {!isOnline && (
                        <span className="text-xs bg-destructive px-1.5 py-0.5 rounded text-white">
                          Offline
                        </span>
                      )}
                    </div>
                    {!isMinimized && (
                      <p className="text-xs opacity-80">
                        {!isOnline ? "No internet connection" : subtitle}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleMinimize}
                    className="h-8 w-8 p-0 text-primary-foreground hover:bg-primary-foreground/20"
                  >
                    {isMinimized ? (
                      <Maximize2 className="h-4 w-4" />
                    ) : (
                      <Minimize2 className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClose}
                    className="h-8 w-8 p-0 text-primary-foreground hover:bg-primary-foreground/20"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Chat Content */}
              {!isMinimized && (
                <>
                  <div className="flex-1 overflow-hidden flex flex-col">
                    <div className="flex-1">
                      <ChatMessageList messages={messages} className="h-full" />
                    </div>
                    {error && (
                      <div className="p-3 border-t">
                        <InlineError
                          error={error}
                          onRetry={() => {
                            setError(null)
                            // Optionally retry the last message
                            if (messages.length > 0) {
                              const lastUserMessage = [...messages]
                                .reverse()
                                .find(msg => msg.role === "user")
                              if (lastUserMessage) {
                                handleSendMessage(lastUserMessage.content)
                              }
                            }
                          }}
                          onDismiss={() => setError(null)}
                        />
                      </div>
                    )}
                  </div>
                  <ChatInput
                    onSendMessage={handleSendMessage}
                    disabled={disabled || isLoading || !isOnline}
                    placeholder={!isOnline ? "No internet connection..." : placeholder}
                  />
                </>
              )}
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Toggle Button */}
      <motion.div
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Button
          onClick={handleToggleChat}
          className="h-14 w-14 rounded-full shadow-lg relative"
          size="sm"
        >
          <AnimatePresence mode="wait">
            {isOpen ? (
              <motion.div
                key="close"
                initial={{ rotate: -90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: 90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <X className="h-6 w-6" />
              </motion.div>
            ) : (
              <motion.div
                key="chat"
                initial={{ rotate: 90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: -90, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="relative"
              >
                <MessageCircle className="h-6 w-6" />
                {unreadCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
                  >
                    {unreadCount > 9 ? "9+" : unreadCount}
                  </Badge>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </Button>
      </motion.div>
    </div>
  )
}
