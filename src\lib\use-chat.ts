"use client"

import * as React from "react"
import { ChatMessage } from "@/components/ui/chat-message"

export interface UseChatOptions {
  apiEndpoint?: string
  systemPrompt?: string
  temperature?: number
  model?: string
  maxMessages?: number
  onError?: (error: Error) => void
  onSuccess?: (message: string) => void
  initialMessages?: ChatMessage[]
}

export interface UseChatReturn {
  messages: ChatMessage[]
  isLoading: boolean
  error: Error | null
  sendMessage: (content: string) => Promise<void>
  clearMessages: () => void
  reload: () => void
  conversationId: string | null
}

export function useChat({
  apiEndpoint = "/api/chat",
  systemPrompt = "You are a helpful AI assistant.",
  temperature = 0.7,
  model = "gpt-3.5-turbo",
  maxMessages = 50,
  onError,
  onSuccess,
  initialMessages = [],
}: UseChatOptions = {}): UseChatReturn {
  const [messages, setMessages] = React.useState<ChatMessage[]>(initialMessages)
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState<Error | null>(null)
  const [conversationId, setConversationId] = React.useState<string | null>(null)

  const sendMessage = React.useCallback(async (content: string) => {
    if (!content.trim()) return

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      content: content.trim(),
      role: "user",
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(apiEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: content.trim(),
          conversationId,
          config: {
            systemPrompt,
            temperature,
            model,
            maxMessages,
          },
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          errorData.error || `HTTP error! status: ${response.status}`
        )
      }

      const data = await response.json()

      if (!data.message) {
        throw new Error("No message received from API")
      }

      const assistantMessage: ChatMessage = {
        id: `assistant-${Date.now()}`,
        content: data.message,
        role: "assistant",
        timestamp: new Date(),
      }

      setMessages(prev => [...prev, assistantMessage])
      setConversationId(data.conversationId || conversationId)
      
      onSuccess?.(data.message)
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Unknown error occurred")
      setError(error)
      onError?.(error)

      // Add error message to chat
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        content: "Sorry, I encountered an error. Please try again.",
        role: "assistant",
        timestamp: new Date(),
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }, [
    apiEndpoint,
    conversationId,
    systemPrompt,
    temperature,
    model,
    maxMessages,
    onError,
    onSuccess,
  ])

  const clearMessages = React.useCallback(() => {
    setMessages([])
    setConversationId(null)
    setError(null)
  }, [])

  const reload = React.useCallback(() => {
    if (messages.length === 0) return

    // Find the last user message and resend it
    const lastUserMessage = [...messages]
      .reverse()
      .find(msg => msg.role === "user")

    if (lastUserMessage) {
      // Remove messages after the last user message
      const lastUserIndex = messages.findIndex(msg => msg.id === lastUserMessage.id)
      setMessages(messages.slice(0, lastUserIndex + 1))
      setError(null)
      
      // Resend the message
      sendMessage(lastUserMessage.content)
    }
  }, [messages, sendMessage])

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
    reload,
    conversationId,
  }
}

// Hook for managing chat widget state
export interface UseChatWidgetOptions {
  defaultOpen?: boolean
  persistState?: boolean
  storageKey?: string
}

export interface UseChatWidgetReturn {
  isOpen: boolean
  unreadCount: number
  toggleChat: () => void
  openChat: () => void
  closeChat: () => void
  markAsRead: () => void
  incrementUnread: () => void
}

export function useChatWidget({
  defaultOpen = false,
  persistState = true,
  storageKey = "chat-widget-state",
}: UseChatWidgetOptions = {}): UseChatWidgetReturn {
  const [isOpen, setIsOpen] = React.useState(() => {
    if (persistState && typeof window !== "undefined") {
      try {
        const saved = localStorage.getItem(storageKey)
        if (saved) {
          const { isOpen: savedIsOpen } = JSON.parse(saved)
          return savedIsOpen ?? defaultOpen
        }
      } catch (error) {
        console.warn("Failed to load chat widget state:", error)
      }
    }
    return defaultOpen
  })

  const [unreadCount, setUnreadCount] = React.useState(0)

  // Persist state when it changes
  React.useEffect(() => {
    if (persistState && typeof window !== "undefined") {
      try {
        localStorage.setItem(
          storageKey,
          JSON.stringify({ isOpen })
        )
      } catch (error) {
        console.warn("Failed to persist chat widget state:", error)
      }
    }
  }, [isOpen, persistState, storageKey])

  const toggleChat = React.useCallback(() => {
    setIsOpen(prev => {
      const newIsOpen = !prev
      if (newIsOpen) {
        setUnreadCount(0) // Clear unread count when opening
      }
      return newIsOpen
    })
  }, [])

  const openChat = React.useCallback(() => {
    setIsOpen(true)
    setUnreadCount(0)
  }, [])

  const closeChat = React.useCallback(() => {
    setIsOpen(false)
  }, [])

  const markAsRead = React.useCallback(() => {
    setUnreadCount(0)
  }, [])

  const incrementUnread = React.useCallback(() => {
    if (!isOpen) {
      setUnreadCount(prev => prev + 1)
    }
  }, [isOpen])

  return {
    isOpen,
    unreadCount,
    toggleChat,
    openChat,
    closeChat,
    markAsRead,
    incrementUnread,
  }
}
