"use client"

import * as React from "react"
import { ChatMessage } from "@/components/ui/chat-message"

export interface ChatConfig {
  apiEndpoint: string
  systemPrompt: string
  maxMessages: number
  temperature: number
  model: string
  title: string
  subtitle: string
  placeholder: string
  position: "bottom-right" | "bottom-left" | "top-right" | "top-left"
  theme: {
    primaryColor: string
    backgroundColor: string
    textColor: string
  }
}

export interface ChatState {
  messages: ChatMessage[]
  isLoading: boolean
  isOpen: boolean
  unreadCount: number
  config: ChatConfig
  conversationId: string | null
}

export interface ChatContextType {
  state: ChatState
  sendMessage: (content: string) => Promise<void>
  clearMessages: () => void
  toggleChat: () => void
  setConfig: (config: Partial<ChatConfig>) => void
  markAsRead: () => void
}

const defaultConfig: ChatConfig = {
  apiEndpoint: "/api/chat",
  systemPrompt: "You are a helpful AI assistant. Provide clear, concise, and helpful responses to user questions.",
  maxMessages: 50,
  temperature: 0.7,
  model: "gpt-3.5-turbo",
  title: "AI Assistant",
  subtitle: "Ask me anything!",
  placeholder: "Type your message...",
  position: "bottom-right",
  theme: {
    primaryColor: "#000000",
    backgroundColor: "#ffffff",
    textColor: "#000000",
  },
}

const defaultState: ChatState = {
  messages: [],
  isLoading: false,
  isOpen: false,
  unreadCount: 0,
  config: defaultConfig,
  conversationId: null,
}

const ChatContext = React.createContext<ChatContextType | undefined>(undefined)

export function useChatContext() {
  const context = React.useContext(ChatContext)
  if (!context) {
    throw new Error("useChatContext must be used within a ChatProvider")
  }
  return context
}

interface ChatProviderProps {
  children: React.ReactNode
  initialConfig?: Partial<ChatConfig>
  persistMessages?: boolean
  storageKey?: string
}

export function ChatProvider({
  children,
  initialConfig = {},
  persistMessages = true,
  storageKey = "chat-messages",
}: ChatProviderProps) {
  const [state, setState] = React.useState<ChatState>(() => {
    const config = { ...defaultConfig, ...initialConfig }
    
    // Load persisted messages if enabled
    if (persistMessages && typeof window !== "undefined") {
      try {
        const saved = localStorage.getItem(storageKey)
        if (saved) {
          const { messages, conversationId } = JSON.parse(saved)
          return {
            ...defaultState,
            config,
            messages: messages.map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp),
            })),
            conversationId,
          }
        }
      } catch (error) {
        console.warn("Failed to load persisted chat messages:", error)
      }
    }
    
    return { ...defaultState, config }
  })

  // Persist messages when they change
  React.useEffect(() => {
    if (persistMessages && typeof window !== "undefined") {
      try {
        localStorage.setItem(
          storageKey,
          JSON.stringify({
            messages: state.messages,
            conversationId: state.conversationId,
          })
        )
      } catch (error) {
        console.warn("Failed to persist chat messages:", error)
      }
    }
  }, [state.messages, state.conversationId, persistMessages, storageKey])

  const sendMessage = React.useCallback(async (content: string) => {
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content,
      role: "user",
      timestamp: new Date(),
    }

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      isLoading: true,
    }))

    try {
      const response = await fetch(state.config.apiEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: content,
          conversationId: state.conversationId,
          config: {
            systemPrompt: state.config.systemPrompt,
            temperature: state.config.temperature,
            model: state.config.model,
            maxMessages: state.config.maxMessages,
          },
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      const assistantMessage: ChatMessage = {
        id: Date.now().toString() + "-assistant",
        content: data.message,
        role: "assistant",
        timestamp: new Date(),
      }

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, assistantMessage],
        isLoading: false,
        conversationId: data.conversationId || prev.conversationId,
        unreadCount: prev.isOpen ? prev.unreadCount : prev.unreadCount + 1,
      }))
    } catch (error) {
      console.error("Failed to send message:", error)
      
      const errorMessage: ChatMessage = {
        id: Date.now().toString() + "-error",
        content: "Sorry, I encountered an error. Please try again.",
        role: "assistant",
        timestamp: new Date(),
      }

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, errorMessage],
        isLoading: false,
      }))
    }
  }, [state.config, state.conversationId])

  const clearMessages = React.useCallback(() => {
    setState(prev => ({
      ...prev,
      messages: [],
      conversationId: null,
      unreadCount: 0,
    }))
  }, [])

  const toggleChat = React.useCallback(() => {
    setState(prev => ({
      ...prev,
      isOpen: !prev.isOpen,
      unreadCount: !prev.isOpen ? 0 : prev.unreadCount,
    }))
  }, [])

  const setConfig = React.useCallback((newConfig: Partial<ChatConfig>) => {
    setState(prev => ({
      ...prev,
      config: { ...prev.config, ...newConfig },
    }))
  }, [])

  const markAsRead = React.useCallback(() => {
    setState(prev => ({
      ...prev,
      unreadCount: 0,
    }))
  }, [])

  const contextValue: ChatContextType = {
    state,
    sendMessage,
    clearMessages,
    toggleChat,
    setConfig,
    markAsRead,
  }

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  )
}
