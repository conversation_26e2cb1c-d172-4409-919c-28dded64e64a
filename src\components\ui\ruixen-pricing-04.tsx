"use client";

import { cn } from "@/lib/utils";
import NumberFlow from "@number-flow/react";
import { AnimatePresence, motion } from "framer-motion";
import { CheckIcon } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";

type Plan = "monthly" | "annually";

type PLAN = {
    id: string;
    title: string;
    desc: string;
    monthlyPrice: number;
    annuallyPrice: number;
    badge?: string;
    buttonText: string;
    features: string[];
    link: string;
};
export const PLANS: PLAN[] = [
  {
    id: "standard",
    title: "Freemium",
    desc: "Үндсэн өгөгдлийн багцуудад хандах боломжтой. Шинэ хэрэглэгчид болон жижиг төслүүдэд тохиромжтой.",
    monthlyPrice: 0,
    annuallyPrice: 0,
    buttonText: "Үнэгүй эхлэх",
    features: [
      "Үндсэн өгөгдлийн багцуудад хандах эрх",
      "Хязгаарлагдмал AI туслах үйлчилгээ",
      "Үндсэн график харуулах боломж",
      "Олон нийтийн дэмжлэг",
      "Сарын 100 API дуудлага",
      "Үндсэн экспорт боломж",
      "Нийгэмлэгийн форум"
    ],
    link: "#"
  },
  {
    id: "mastermind",
    title: "Pro",
    desc: "Ихэнх өгөгдлийн багцуудад бүрэн хандах эрх, бүрэн AI туслах үйлчилгээ. Мэргэжлийн хэрэглэгчид болон жижиг компаниудад тохиромжтой.",
    monthlyPrice: 99,
    annuallyPrice: 990,
    badge: "Хамгийн алдартай",
    buttonText: "Pro болох",
    features: [
      "Ихэнх өгөгдлийн багцуудад бүрэн хандах эрх",
      "Бүрэн AI туслах үйлчилгээ",
      "Дэвшилтэт график болон дүн шинжилгээ",
      "Экспорт боломжууд (CSV, JSON, PDF)",
      "Сарын 10,000 API дуудлага",
      "Тэргүүлэх дэмжлэг",
      "Захиалгат судалгааны кредит"
    ],
    link: "#"
  },
  {
    id: "enterprise",
    title: "Enterprise",
    desc: "Бүх өгөгдлийн багцуудад бүрэн хандах эрх, тэргүүлэх дэмжлэг, API хандалт, захиалгат судалгааны кредит. Том компаниудад тохиромжтой.",
    monthlyPrice: 499,
    annuallyPrice: 4990,
    badge: "Бизнест зориулсан",
    buttonText: "Enterprise болох",
    features: [
      "Бүх өгөгдлийн багцуудад бүрэн хандах эрх",
      "Хязгааргүй AI туслах үйлчилгээ",
      "Тэргүүлэх дэмжлэг",
      "Бүрэн API хандалт",
      "Захиалгат судалгааны кредит",
      "Багийн хамтын ажиллагааны хэрэгсэл",
      "Тусгай сургалт болон зөвлөгөө"
    ],
    link: "#"
  },
];

export default function Pricing_04() {

    const [billPlan, setBillPlan] = useState<Plan>("monthly");

    const handleSwitch = () => {
        setBillPlan((prev) => (prev === "monthly" ? "annually" : "monthly"));
    };

    return (
        <div className="relative flex flex-col items-center justify-center max-w-5xl py-20 mx-auto">

            <div className="flex flex-col items-center justify-center max-w-2xl mx-auto">

                    <div className="flex flex-col items-center text-center max-w-2xl mx-auto">
                        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mt-6">
                            Багцын үнэ
                        </h2>
                        <p className="text-base md:text-lg text-center text-accent-foreground/80 mt-6">
                        Танай хэрэгцээнд тохирсон багцыг сонгож, Монголын зах зээлийн өгөгдөлд хурдан хандаж, өгөгдөлд суурилсан шийдвэр гаргаарай.
                        </p>
                    </div>
                    <div className="flex items-center justify-center space-x-4 mt-6">
                        <span className="text-base font-medium">Сарын</span>
                        <button onClick={handleSwitch} className="relative rounded-full focus:outline-none">
                            <div className="w-12 h-6 transition rounded-full shadow-md outline-none bg-blue-500"></div>
                            <div
                                className={cn(
                                    "absolute inline-flex items-center justify-center w-4 h-4 transition-all duration-500 ease-in-out top-1 left-1 rounded-full bg-white",
                                    billPlan === "annually" ? "translate-x-6" : "translate-x-0"
                                )}
                            />
                        </button>
                        <span className="text-base font-medium">Жилийн</span>
                    </div>
            </div>

            <div className="grid w-full grid-cols-1 lg:grid-cols-3 pt-8 lg:pt-12 gap-4 lg:gap-6 max-w-6xl mx-auto">
                {PLANS.map((plan, idx) => (
                        <Plan key={plan.id} plan={plan} billPlan={billPlan} />
                ))}
            </div>
        </div>
    );
};

const Plan = ({ plan, billPlan }: { plan: PLAN, billPlan: Plan }) => {
    return (
        <div className={cn(
            "flex flex-col relative rounded-2xl lg:rounded-3xl transition-all bg-background/ items-start w-full border border-foreground/10 overflow-hidden",
            plan.title === "Pro" && "border-blue-500"
        )}>
            {plan.title === "Pro" && (
                <div className="absolute top-1/2 inset-x-0 mx-auto h-12 -rotate-45 w-full bg-blue-600 rounded-2xl lg:rounded-3xl blur-[8rem] -z-10"></div>
            )}

            <div className="p-4 md:p-8 flex rounded-t-2xl lg:rounded-t-3xl flex-col items-start w-full relative">
                <h2 className="font-medium text-xl text-foreground pt-5">
                    {plan.title}
                </h2>
                <h3 className="mt-3 text-2xl font-bold md:text-5xl">
                    <NumberFlow
                        value={billPlan === "monthly" ? plan.monthlyPrice : plan.annuallyPrice}
                        suffix={billPlan === "monthly" ? "/mo" : "/yr"}
                        format={{
                            currency: "USD",
                            style: "currency",
                            currencySign: "standard",
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                            currencyDisplay: "narrowSymbol"
                        }}
                    />
                </h3>
                <p className="text-sm md:text-base text-muted-foreground mt-2">
                    {plan.desc}
                </p>
            </div>
            <div className="flex flex-col items-start w-full px-4 py-2 md:px-8">
                <Button size="lg" className="w-full">
                    {plan.buttonText}
                </Button>
                <div className="h-8 overflow-hidden w-full mx-auto">
                    <AnimatePresence mode="wait">
                        <motion.span
                            key={billPlan}
                            initial={{ y: 20, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            exit={{ y: -20, opacity: 0 }}
                            transition={{ duration: 0.2, ease: "easeOut" }}
                            className="text-sm text-center text-muted-foreground mt-3 mx-auto block"
                        >
                            {billPlan === "monthly" ? (
                                "Billed monthly"
                            ) : (
                                "Billed in one annual payment"
                            )}
                        </motion.span>
                    </AnimatePresence>
                </div>
            </div>
            <div className="flex flex-col items-start w-full p-5 mb-4 ml-1 gap-y-2">
                <span className="text-base text-left mb-2">
                    Includes: 
                </span>
                {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center justify-start gap-2">
                        <div className="flex items-center justify-center">
                            <CheckIcon className="size-5" />
                        </div>
                        <span>{feature}</span>
                    </div>
                ))}
            </div>
        </div>
    );
};
