# AI-Powered Chatbot Integration

A complete AI-powered chatbot solution integrated into your Next.js application using OpenAI's ChatGPT API. This implementation provides a professional, customizable chat widget with comprehensive error handling, rate limiting, and accessibility features.

## 🚀 Features

### Frontend Components
- **Chat Widget**: Floating chat button with expandable modal interface
- **Message Display**: Clean conversation history with user/bot message differentiation
- **Input Interface**: Text input with send button and Enter key support
- **Loading States**: Typing indicators and loading animations
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Accessibility**: Keyboard navigation and screen reader support

### Backend Integration
- **OpenAI API Integration**: Direct integration with ChatGPT API
- **Rate Limiting**: Built-in protection against API abuse
- **Error Handling**: Comprehensive error management and user feedback
- **Conversation Context**: Maintains conversation state for multi-turn interactions

### Configuration System
- **Customizable AI Personality**: Configure system prompts and behavior
- **Appearance Settings**: Customize colors, position, and styling
- **Feature Toggles**: Enable/disable specific functionality
- **Real-time Updates**: Configuration changes apply immediately

### Technical Features
- **TypeScript**: Full type safety throughout the application
- **Error Boundaries**: React error boundaries for graceful error handling
- **Network Status**: Offline detection and user feedback
- **Persistent Storage**: Optional conversation history persistence
- **Responsive UI**: Tailwind CSS with shadcn/ui components

## 📁 File Structure

```
src/
├── app/
│   ├── api/
│   │   └── chat/
│   │       ├── route.ts              # Main chat API endpoint
│   │       └── config/
│   │           └── route.ts          # Configuration API endpoint
│   ├── chat-demo/
│   │   └── page.tsx                  # Demo page for testing
│   └── layout.tsx                    # Updated with ChatProvider
├── components/
│   └── ui/
│       ├── chat-widget.tsx           # Main chat widget component
│       ├── chat-message.tsx          # Message display components
│       ├── chat-input.tsx            # Input field component
│       ├── chat-config.tsx           # Configuration interface
│       ├── chat-provider.tsx         # Main provider component
│       ├── chat-error-boundary.tsx   # Error handling components
│       └── dialog.tsx                # Dialog component (if not existing)
├── lib/
│   ├── chat-context.tsx              # React context for chat state
│   └── use-chat.ts                   # Custom hooks for chat functionality
└── .env.example                      # Environment variables template
```

## 🛠️ Setup Instructions

### 1. Install Dependencies

The required dependencies are already installed:
- `openai` - OpenAI API client
- `@radix-ui/react-dialog` - Dialog components (already in your project)
- `lucide-react` - Icons (already in your project)
- `framer-motion` - Animations (already in your project)

### 2. Configure Environment Variables

1. Copy the environment template:
   ```bash
   cp .env.example .env.local
   ```

2. Add your OpenAI API key:
   ```env
   OPENAI_API_KEY=your_openai_api_key_here
   ```

   Get your API key from: https://platform.openai.com/api-keys

### 3. Test the Implementation

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Visit the demo page:
   ```
   http://localhost:3000/chat-demo
   ```

3. Click the chat button in the bottom-right corner to test the chatbot

## 🎨 Customization

### Basic Configuration

The chatbot can be customized through the configuration interface or programmatically:

```tsx
import { ChatProvider } from "@/components/ui/chat-provider"

export default function MyApp() {
  return (
    <ChatProvider
      initialConfig={{
        title: "Customer Support",
        subtitle: "How can we help you?",
        systemPrompt: "You are a helpful customer support assistant...",
        position: "bottom-right",
        theme: {
          primaryColor: "#0066cc",
          backgroundColor: "#ffffff",
          textColor: "#000000",
        }
      }}
    >
      {/* Your app content */}
    </ChatProvider>
  )
}
```

### Advanced Customization

#### Custom System Prompts
Configure the AI's personality and behavior:

```typescript
const customPrompt = `
You are a helpful customer support assistant for [Your Company].
- Be friendly and professional
- Provide accurate information about our products/services
- If you don't know something, direct users to contact human support
- Keep responses concise but helpful
`
```

#### Styling and Theming
The chatbot uses your existing Tailwind CSS configuration and can be styled to match your brand:

```tsx
// Custom theme configuration
const theme = {
  primaryColor: "#your-brand-color",
  backgroundColor: "#ffffff",
  textColor: "#333333",
}
```

### Widget Positioning
Choose from four positions:
- `bottom-right` (default)
- `bottom-left`
- `top-right`
- `top-left`

## 🔧 API Configuration

### Rate Limiting
The API includes built-in rate limiting:
- Default: 10 requests per minute per IP
- Configurable through environment variables
- Returns 429 status code when exceeded

### Error Handling
Comprehensive error handling for:
- Network connectivity issues
- API authentication errors
- Rate limit exceeded
- Server errors
- Invalid requests

### Security Features
- Input validation and sanitization
- Rate limiting protection
- Error message sanitization
- CORS protection (if needed)

## 🧪 Testing

### Manual Testing
1. Visit `/chat-demo` to test the chatbot
2. Try various message types and lengths
3. Test error scenarios (disconnect internet, invalid API key)
4. Test configuration changes

### Error Scenarios to Test
- Network disconnection
- Invalid API key
- Rate limit exceeded
- Long messages
- Special characters
- Empty messages

## 🚀 Deployment

### Environment Variables
Ensure these are set in your production environment:
```env
OPENAI_API_KEY=your_production_api_key
NODE_ENV=production
```

### Performance Considerations
- The chatbot uses client-side state management
- Conversation history is stored locally (optional)
- API calls are made directly from the client to your API routes
- Consider implementing server-side conversation storage for production

## 🔍 Troubleshooting

### Common Issues

1. **"OpenAI API key not configured"**
   - Ensure `.env.local` file exists with valid `OPENAI_API_KEY`
   - Restart your development server after adding environment variables

2. **"Rate limit exceeded"**
   - Wait a minute before trying again
   - Check your OpenAI API usage limits
   - Consider upgrading your OpenAI plan

3. **Chat widget not appearing**
   - Ensure `ChatProvider` is properly wrapped around your app
   - Check browser console for JavaScript errors
   - Verify all required dependencies are installed

4. **Network errors**
   - Check internet connectivity
   - Verify API endpoints are accessible
   - Check browser network tab for failed requests

### Debug Mode
In development, detailed error information is available in:
- Browser console
- Network tab (for API calls)
- Error boundary fallback UI

## 📝 Next Steps

### Potential Enhancements
1. **Conversation Persistence**: Store conversations in a database
2. **User Authentication**: Associate conversations with user accounts
3. **File Upload**: Allow users to upload images or documents
4. **Multi-language Support**: Internationalization for global users
5. **Analytics**: Track usage patterns and popular questions
6. **Admin Dashboard**: Manage conversations and monitor performance
7. **Custom Integrations**: Connect with your existing support systems

### Production Considerations
1. **Database Integration**: Store conversations and user data
2. **Monitoring**: Add error tracking and performance monitoring
3. **Scaling**: Consider API caching and load balancing
4. **Security**: Implement proper authentication and authorization
5. **Compliance**: Ensure GDPR/privacy compliance for stored conversations

## 📞 Support

If you encounter any issues or need assistance with the chatbot implementation, please refer to:
- This documentation
- The demo page at `/chat-demo`
- OpenAI API documentation: https://platform.openai.com/docs
- Next.js documentation: https://nextjs.org/docs

The chatbot is now fully integrated and ready for use! 🎉
