import { MarketResearchCards } from "@/components/ui/market-research-cards";
import { GlowingEffectDemo } from "@/components/ui/glowing-effect-demo";
import { FeaturesSectionWithHoverEffectsDemo } from "@/components/ui/feature-section-demo";
import { MarqueeDemo } from "@/components/ui/marquee-demo";
import { default as RuixenPricingDemo } from "@/components/ui/ruixen-pricing-demo";
import { default as TextHoverEffectDemo } from "@/components/ui/text-hover-effect-demo";
import { default as FooterSectionDemo } from "@/components/ui/footer-section-demo";

export default function DemoPage() {
  return (
    <main className="min-h-screen bg-background">
      <div className="container mx-auto py-12 space-y-16">
        {/* New Feature Section with Hover Effects */}
        <section>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Features Section with Hover Effects
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              A modern feature grid with beautiful hover animations and Tabler icons.
            </p>
          </div>
          <FeaturesSectionWithHoverEffectsDemo />
        </section>

        {/* Divider */}
        <div className="border-t border-border"></div>

        {/* Marquee Demo Section */}
        <section>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Marquee Testimonials Section
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Smooth scrolling marquee with technology logos and brand partners.
            </p>
          </div>
          <MarqueeDemo />
        </section>

        {/* Divider */}
        <div className="border-t border-border"></div>

        {/* Ruixen Pricing Section */}
        <section>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Ruixen Pricing Component
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Modern pricing component with animated number flow and monthly/annual toggle.
            </p>
          </div>
          <RuixenPricingDemo />
        </section>

        {/* Divider */}
        <div className="border-t border-border"></div>

        {/* Market Research Cards Section */}
        <section>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Market Research Cards
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Interactive cards showcasing market research services.
            </p>
          </div>
          <MarketResearchCards />
        </section>

        {/* Divider */}
        <div className="border-t border-border"></div>

        {/* Text Hover Effect Demo Section */}
        <section>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Text Hover Effect
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Interactive text with gradient reveal effect on mouse hover and stroke animation.
            </p>
          </div>
          <TextHoverEffectDemo />
        </section>

        {/* Divider */}
        <div className="border-t border-border"></div>

        {/* Footer Section Demo */}
        <section>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Modern Footer Section
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Animated footer with rounded corners, gradient background, and social links.
            </p>
          </div>
          <FooterSectionDemo />
        </section>

        {/* Divider */}
        <div className="border-t border-border"></div>

        {/* Original Demo Section */}
        <section className="px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Original Glowing Effect Demo
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              The original demo component showcasing the glowing effect capabilities.
            </p>
          </div>
          <GlowingEffectDemo />
        </section>
      </div>
    </main>
  );
}
