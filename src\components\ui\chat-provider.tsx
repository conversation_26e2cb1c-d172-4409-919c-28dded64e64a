"use client"

import * as React from "react"
import { ChatWidget } from "./chat-widget"
import { ChatConfig, ChatConfigData } from "./chat-config"
import { useChat } from "@/lib/use-chat"
import { ChatMessage } from "./chat-message"
import { ChatErrorBoundary } from "./chat-error-boundary"

interface ChatProviderProps {
  children: React.ReactNode
  initialConfig?: Partial<ChatConfigData>
  showConfig?: boolean
  onConfigChange?: (config: ChatConfigData) => void
}

const defaultConfig: ChatConfigData = {
  systemPrompt: "You are a helpful AI assistant for customer support. Provide clear, concise, and helpful responses to user questions. Be friendly and professional.",
  temperature: 0.7,
  model: "gpt-3.5-turbo",
  maxMessages: 50,
  title: "AI Assistant",
  subtitle: "How can I help you today?",
  placeholder: "Type your message...",
  position: "bottom-right",
  theme: {
    primaryColor: "#000000",
    backgroundColor: "#ffffff",
    textColor: "#000000",
  },
  features: {
    persistMessages: true,
    showTypingIndicator: true,
    showTimestamps: true,
    allowFileUpload: false,
    maxFileSize: 5 * 1024 * 1024,
  },
}

export function ChatProvider({
  children,
  initialConfig = {},
  showConfig = false,
  onConfigChange,
}: ChatProviderProps) {
  const [config, setConfig] = React.useState<ChatConfigData>({
    ...defaultConfig,
    ...initialConfig,
  })

  const [isConfigOpen, setIsConfigOpen] = React.useState(showConfig)

  const { messages, isLoading, sendMessage, clearMessages } = useChat({
    apiEndpoint: "/api/chat",
    systemPrompt: config.systemPrompt,
    temperature: config.temperature,
    model: config.model,
    maxMessages: config.maxMessages,
  })

  const handleConfigChange = React.useCallback((newConfig: Partial<ChatConfigData>) => {
    const updatedConfig = {
      ...config,
      ...newConfig,
      theme: newConfig.theme ? { ...config.theme, ...newConfig.theme } : config.theme,
      features: newConfig.features ? { ...config.features, ...newConfig.features } : config.features,
    }
    setConfig(updatedConfig)
    onConfigChange?.(updatedConfig)
  }, [config, onConfigChange])

  const handleSendMessage = React.useCallback(async (content: string): Promise<string> => {
    await sendMessage(content)
    // Return empty string as the actual response is handled by the useChat hook
    return ""
  }, [sendMessage])

  const saveConfig = React.useCallback(async () => {
    try {
      const response = await fetch("/api/chat/config", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(config),
      })

      if (!response.ok) {
        throw new Error("Failed to save configuration")
      }

      // You could show a success toast here
      console.log("Configuration saved successfully")
    } catch (error) {
      console.error("Failed to save configuration:", error)
      // You could show an error toast here
    }
  }, [config])

  const resetConfig = React.useCallback(async () => {
    try {
      const response = await fetch("/api/chat/config", {
        method: "PUT",
      })

      if (!response.ok) {
        throw new Error("Failed to reset configuration")
      }

      const data = await response.json()
      setConfig(data.config)
      onConfigChange?.(data.config)
    } catch (error) {
      console.error("Failed to reset configuration:", error)
      // Fallback to default config
      setConfig(defaultConfig)
      onConfigChange?.(defaultConfig)
    }
  }, [onConfigChange])

  return (
    <ChatErrorBoundary
      onError={(error, errorInfo) => {
        console.error("Chat Provider Error:", error, errorInfo)
        // You could send this to an error reporting service
      }}
    >
      {children}

      {isConfigOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-background rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <ChatConfig
              config={config}
              onConfigChange={handleConfigChange}
              onSave={saveConfig}
              onReset={resetConfig}
            />
            <div className="p-4 border-t flex justify-end">
              <button
                onClick={() => setIsConfigOpen(false)}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      <ChatWidget
        title={config.title}
        subtitle={config.subtitle}
        placeholder={config.placeholder}
        position={config.position}
        onSendMessage={handleSendMessage}
        initialMessages={messages}
        disabled={isLoading}
      />
    </ChatErrorBoundary>
  )
}
