"use client"

import * as React from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, MessageCircle } from "lucide-react"
import { Button } from "./button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./card"

interface ChatErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
}

interface ChatErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{
    error: Error | null
    resetError: () => void
  }>
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

export class ChatErrorBoundary extends React.Component<
  ChatErrorBoundaryProps,
  ChatErrorBoundaryState
> {
  constructor(props: ChatErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ChatErrorBoundaryState> {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    })

    // Log error to console in development
    if (process.env.NODE_ENV === "development") {
      console.error("Chat Error Boundary caught an error:", error, errorInfo)
    }

    // Call optional error handler
    this.props.onError?.(error, errorInfo)
  }

  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />
      }

      return <DefaultErrorFallback error={this.state.error} resetError={this.resetError} />
    }

    return this.props.children
  }
}

interface DefaultErrorFallbackProps {
  error: Error | null
  resetError: () => void
}

function DefaultErrorFallback({ error, resetError }: DefaultErrorFallbackProps) {
  return (
    <Card className="w-80 sm:w-96 border-destructive">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-destructive">
          <AlertTriangle className="h-5 w-5" />
          Chat Error
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          Something went wrong with the chat widget. This might be due to:
        </p>
        <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
          <li>Network connectivity issues</li>
          <li>API configuration problems</li>
          <li>Temporary service unavailability</li>
        </ul>
        
        {process.env.NODE_ENV === "development" && error && (
          <details className="text-xs">
            <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
              Error Details (Development)
            </summary>
            <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto max-h-32">
              {error.message}
              {error.stack && `\n\n${error.stack}`}
            </pre>
          </details>
        )}

        <div className="flex gap-2">
          <Button onClick={resetError} size="sm" className="flex-1">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => window.location.reload()}
          >
            Reload Page
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Hook for handling async errors in components
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null)

  const handleError = React.useCallback((error: Error) => {
    console.error("Async error caught:", error)
    setError(error)
  }, [])

  const clearError = React.useCallback(() => {
    setError(null)
  }, [])

  return {
    error,
    handleError,
    clearError,
  }
}

// Component for displaying inline errors
interface InlineErrorProps {
  error: Error | string | null
  onRetry?: () => void
  onDismiss?: () => void
  className?: string
}

export function InlineError({ error, onRetry, onDismiss, className }: InlineErrorProps) {
  if (!error) return null

  const errorMessage = typeof error === "string" ? error : error.message

  return (
    <div className={`flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md ${className}`}>
      <AlertTriangle className="h-4 w-4 text-destructive flex-shrink-0" />
      <p className="text-sm text-destructive flex-1">{errorMessage}</p>
      <div className="flex gap-1">
        {onRetry && (
          <Button variant="ghost" size="sm" onClick={onRetry}>
            <RefreshCw className="h-3 w-3" />
          </Button>
        )}
        {onDismiss && (
          <Button variant="ghost" size="sm" onClick={onDismiss}>
            ×
          </Button>
        )}
      </div>
    </div>
  )
}

// Loading states component
interface LoadingStateProps {
  type?: "spinner" | "dots" | "pulse"
  message?: string
  className?: string
}

export function LoadingState({ 
  type = "dots", 
  message = "Loading...", 
  className 
}: LoadingStateProps) {
  const renderLoader = () => {
    switch (type) {
      case "spinner":
        return (
          <RefreshCw className="h-4 w-4 animate-spin" />
        )
      case "pulse":
        return (
          <div className="flex space-x-1">
            <div className="h-2 w-2 bg-current rounded-full animate-pulse"></div>
            <div className="h-2 w-2 bg-current rounded-full animate-pulse [animation-delay:0.2s]"></div>
            <div className="h-2 w-2 bg-current rounded-full animate-pulse [animation-delay:0.4s]"></div>
          </div>
        )
      case "dots":
      default:
        return (
          <div className="flex space-x-1">
            <div className="h-2 w-2 bg-current rounded-full animate-bounce"></div>
            <div className="h-2 w-2 bg-current rounded-full animate-bounce [animation-delay:0.1s]"></div>
            <div className="h-2 w-2 bg-current rounded-full animate-bounce [animation-delay:0.2s]"></div>
          </div>
        )
    }
  }

  return (
    <div className={`flex items-center gap-2 text-muted-foreground ${className}`}>
      {renderLoader()}
      <span className="text-sm">{message}</span>
    </div>
  )
}

// Network status hook
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = React.useState(
    typeof navigator !== "undefined" ? navigator.onLine : true
  )

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
    }
  }, [])

  return isOnline
}
