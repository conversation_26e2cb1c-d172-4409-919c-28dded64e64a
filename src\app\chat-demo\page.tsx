"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardHeader, CardContent, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ChatConfig, ChatConfigData } from "@/components/ui/chat-config"
import { 
  MessageCircle, 
  Settings, 
  Zap, 
  Shield, 
  Clock,
  Users,
  CheckCircle
} from "lucide-react"

export default function ChatDemoPage() {
  const [showConfig, setShowConfig] = React.useState(false)
  const [config, setConfig] = React.useState<ChatConfigData>({
    systemPrompt: "You are a helpful AI assistant for customer support. Provide clear, concise, and helpful responses to user questions. Be friendly and professional.",
    temperature: 0.7,
    model: "gpt-3.5-turbo",
    maxMessages: 50,
    title: "AI Assistant",
    subtitle: "How can I help you today?",
    placeholder: "Type your message...",
    position: "bottom-right",
    theme: {
      primaryColor: "#000000",
      backgroundColor: "#ffffff",
      textColor: "#000000",
    },
    features: {
      persistMessages: true,
      showTypingIndicator: true,
      showTimestamps: true,
      allowFileUpload: false,
      maxFileSize: 5 * 1024 * 1024,
    },
  })

  const handleConfigChange = (newConfig: Partial<ChatConfigData>) => {
    setConfig(prev => ({
      ...prev,
      ...newConfig,
      theme: newConfig.theme ? { ...prev.theme, ...newConfig.theme } : prev.theme,
      features: newConfig.features ? { ...prev.features, ...newConfig.features } : prev.features,
    }))
  }

  const saveConfig = async () => {
    try {
      const response = await fetch("/api/chat/config", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(config),
      })

      if (!response.ok) {
        throw new Error("Failed to save configuration")
      }

      alert("Configuration saved successfully!")
    } catch (error) {
      console.error("Failed to save configuration:", error)
      alert("Failed to save configuration. Please try again.")
    }
  }

  const resetConfig = async () => {
    try {
      const response = await fetch("/api/chat/config", {
        method: "PUT",
      })

      if (!response.ok) {
        throw new Error("Failed to reset configuration")
      }

      const data = await response.json()
      setConfig(data.config)
      alert("Configuration reset to defaults!")
    } catch (error) {
      console.error("Failed to reset configuration:", error)
      alert("Failed to reset configuration. Please try again.")
    }
  }

  const features = [
    {
      icon: <MessageCircle className="h-6 w-6" />,
      title: "Smart Conversations",
      description: "AI-powered responses that understand context and provide helpful answers"
    },
    {
      icon: <Zap className="h-6 w-6" />,
      title: "Real-time Responses",
      description: "Fast, responsive chat experience with typing indicators and instant replies"
    },
    {
      icon: <Shield className="h-6 w-6" />,
      title: "Secure & Private",
      description: "Rate limiting, input validation, and secure API endpoints"
    },
    {
      icon: <Settings className="h-6 w-6" />,
      title: "Fully Customizable",
      description: "Configure appearance, behavior, and AI personality to match your brand"
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: "Persistent History",
      description: "Conversation history is saved locally for seamless user experience"
    },
    {
      icon: <Users className="h-6 w-6" />,
      title: "Accessible Design",
      description: "Built with accessibility in mind, keyboard navigation and screen reader support"
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">AI Chatbot Demo</h1>
              <p className="text-muted-foreground mt-2">
                Experience our AI-powered customer support chatbot
              </p>
            </div>
            <Button onClick={() => setShowConfig(true)}>
              <Settings className="h-4 w-4 mr-2" />
              Configure Chatbot
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">
            <Zap className="h-3 w-3 mr-1" />
            Powered by OpenAI GPT
          </Badge>
          <h2 className="text-4xl font-bold mb-4">
            Intelligent Customer Support
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Our AI chatbot provides instant, helpful responses to customer questions. 
            Try it out by clicking the chat button in the bottom-right corner!
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
          {features.map((feature, index) => (
            <Card key={index} className="h-full">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg text-primary">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Setup Instructions */}
        <Card className="mb-16">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Setup Instructions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                1
              </div>
              <div>
                <h4 className="font-medium">Add OpenAI API Key</h4>
                <p className="text-sm text-muted-foreground">
                  Create a <code className="bg-muted px-1 rounded">.env.local</code> file and add your OpenAI API key:
                </p>
                <code className="block bg-muted p-2 rounded mt-2 text-sm">
                  OPENAI_API_KEY=your_api_key_here
                </code>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                2
              </div>
              <div>
                <h4 className="font-medium">Test the Chatbot</h4>
                <p className="text-sm text-muted-foreground">
                  Click the chat button in the bottom-right corner to start a conversation with the AI assistant.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                3
              </div>
              <div>
                <h4 className="font-medium">Customize Configuration</h4>
                <p className="text-sm text-muted-foreground">
                  Use the "Configure Chatbot" button above to customize the AI's behavior, appearance, and features.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Current Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Model:</span> {config.model}
              </div>
              <div>
                <span className="font-medium">Temperature:</span> {config.temperature}
              </div>
              <div>
                <span className="font-medium">Position:</span> {config.position}
              </div>
              <div>
                <span className="font-medium">Max Messages:</span> {config.maxMessages}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Configuration Modal */}
      {showConfig && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-background rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <ChatConfig
              config={config}
              onConfigChange={handleConfigChange}
              onSave={saveConfig}
              onReset={resetConfig}
            />
            <div className="p-4 border-t flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowConfig(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowConfig(false)}>
                Apply Changes
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
