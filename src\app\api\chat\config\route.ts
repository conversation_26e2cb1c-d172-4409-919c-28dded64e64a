import { NextRequest, NextResponse } from "next/server"

// Default configuration
const DEFAULT_CONFIG = {
  systemPrompt: "You are a helpful AI assistant for customer support. Provide clear, concise, and helpful responses to user questions. Be friendly and professional. If you don't know something, admit it and suggest how the user might get help.",
  temperature: 0.7,
  model: "gpt-3.5-turbo",
  maxMessages: 50,
  title: "AI Assistant",
  subtitle: "How can I help you today?",
  placeholder: "Type your message...",
  position: "bottom-right" as const,
  theme: {
    primaryColor: "#000000",
    backgroundColor: "#ffffff",
    textColor: "#000000",
  },
  features: {
    persistMessages: true,
    showTypingIndicator: true,
    showTimestamps: true,
    allowFileUpload: false,
    maxFileSize: 5 * 1024 * 1024, // 5MB
  },
  rateLimit: {
    requests: 10,
    windowMs: 60 * 1000, // 1 minute
  },
}

interface ChatConfig {
  systemPrompt: string
  temperature: number
  model: string
  maxMessages: number
  title: string
  subtitle: string
  placeholder: string
  position: "bottom-right" | "bottom-left" | "top-right" | "top-left"
  theme: {
    primaryColor: string
    backgroundColor: string
    textColor: string
  }
  features: {
    persistMessages: boolean
    showTypingIndicator: boolean
    showTimestamps: boolean
    allowFileUpload: boolean
    maxFileSize: number
  }
  rateLimit: {
    requests: number
    windowMs: number
  }
}

// In a real application, this would be stored in a database
let currentConfig: ChatConfig = { ...DEFAULT_CONFIG }

function validateConfig(data: any): Partial<ChatConfig> | null {
  if (!data || typeof data !== "object") {
    return null
  }

  const validatedConfig: Partial<ChatConfig> = {}

  // Validate systemPrompt
  if (data.systemPrompt && typeof data.systemPrompt === "string" && data.systemPrompt.trim().length > 0) {
    validatedConfig.systemPrompt = data.systemPrompt.trim()
  }

  // Validate temperature
  if (data.temperature && typeof data.temperature === "number" && data.temperature >= 0 && data.temperature <= 2) {
    validatedConfig.temperature = data.temperature
  }

  // Validate model
  if (data.model && typeof data.model === "string") {
    const allowedModels = ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo-preview"]
    if (allowedModels.includes(data.model)) {
      validatedConfig.model = data.model
    }
  }

  // Validate maxMessages
  if (data.maxMessages && typeof data.maxMessages === "number" && data.maxMessages > 0 && data.maxMessages <= 100) {
    validatedConfig.maxMessages = data.maxMessages
  }

  // Validate UI strings
  if (data.title && typeof data.title === "string" && data.title.trim().length > 0) {
    validatedConfig.title = data.title.trim()
  }

  if (data.subtitle && typeof data.subtitle === "string" && data.subtitle.trim().length > 0) {
    validatedConfig.subtitle = data.subtitle.trim()
  }

  if (data.placeholder && typeof data.placeholder === "string" && data.placeholder.trim().length > 0) {
    validatedConfig.placeholder = data.placeholder.trim()
  }

  // Validate position
  if (data.position && typeof data.position === "string") {
    const allowedPositions = ["bottom-right", "bottom-left", "top-right", "top-left"]
    if (allowedPositions.includes(data.position)) {
      validatedConfig.position = data.position as ChatConfig["position"]
    }
  }

  // Validate theme
  if (data.theme && typeof data.theme === "object") {
    const theme: Partial<ChatConfig["theme"]> = {}
    
    if (data.theme.primaryColor && typeof data.theme.primaryColor === "string") {
      // Basic hex color validation
      if (/^#[0-9A-F]{6}$/i.test(data.theme.primaryColor)) {
        theme.primaryColor = data.theme.primaryColor
      }
    }

    if (data.theme.backgroundColor && typeof data.theme.backgroundColor === "string") {
      if (/^#[0-9A-F]{6}$/i.test(data.theme.backgroundColor)) {
        theme.backgroundColor = data.theme.backgroundColor
      }
    }

    if (data.theme.textColor && typeof data.theme.textColor === "string") {
      if (/^#[0-9A-F]{6}$/i.test(data.theme.textColor)) {
        theme.textColor = data.theme.textColor
      }
    }

    if (Object.keys(theme).length > 0) {
      validatedConfig.theme = theme
    }
  }

  // Validate features
  if (data.features && typeof data.features === "object") {
    const features: Partial<ChatConfig["features"]> = {}

    if (typeof data.features.persistMessages === "boolean") {
      features.persistMessages = data.features.persistMessages
    }

    if (typeof data.features.showTypingIndicator === "boolean") {
      features.showTypingIndicator = data.features.showTypingIndicator
    }

    if (typeof data.features.showTimestamps === "boolean") {
      features.showTimestamps = data.features.showTimestamps
    }

    if (typeof data.features.allowFileUpload === "boolean") {
      features.allowFileUpload = data.features.allowFileUpload
    }

    if (data.features.maxFileSize && typeof data.features.maxFileSize === "number" && data.features.maxFileSize > 0) {
      features.maxFileSize = Math.min(data.features.maxFileSize, 10 * 1024 * 1024) // Max 10MB
    }

    if (Object.keys(features).length > 0) {
      validatedConfig.features = features
    }
  }

  // Validate rate limit
  if (data.rateLimit && typeof data.rateLimit === "object") {
    const rateLimit: Partial<ChatConfig["rateLimit"]> = {}

    if (data.rateLimit.requests && typeof data.rateLimit.requests === "number" && data.rateLimit.requests > 0) {
      rateLimit.requests = Math.min(data.rateLimit.requests, 100) // Max 100 requests
    }

    if (data.rateLimit.windowMs && typeof data.rateLimit.windowMs === "number" && data.rateLimit.windowMs > 0) {
      rateLimit.windowMs = Math.max(data.rateLimit.windowMs, 1000) // Min 1 second
    }

    if (Object.keys(rateLimit).length > 0) {
      validatedConfig.rateLimit = rateLimit
    }
  }

  return validatedConfig
}

// GET - Retrieve current configuration
export async function GET() {
  try {
    return NextResponse.json({
      config: currentConfig,
      success: true,
    })
  } catch (error) {
    console.error("Config GET error:", error)
    return NextResponse.json(
      { error: "Failed to retrieve configuration" },
      { status: 500 }
    )
  }
}

// POST - Update configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedConfig = validateConfig(body)

    if (!validatedConfig || Object.keys(validatedConfig).length === 0) {
      return NextResponse.json(
        { error: "Invalid configuration data" },
        { status: 400 }
      )
    }

    // Merge with current config
    currentConfig = {
      ...currentConfig,
      ...validatedConfig,
      theme: validatedConfig.theme ? {
        ...currentConfig.theme,
        ...validatedConfig.theme,
      } : currentConfig.theme,
      features: validatedConfig.features ? {
        ...currentConfig.features,
        ...validatedConfig.features,
      } : currentConfig.features,
      rateLimit: validatedConfig.rateLimit ? {
        ...currentConfig.rateLimit,
        ...validatedConfig.rateLimit,
      } : currentConfig.rateLimit,
    }

    return NextResponse.json({
      config: currentConfig,
      success: true,
      message: "Configuration updated successfully",
    })

  } catch (error) {
    console.error("Config POST error:", error)
    return NextResponse.json(
      { error: "Failed to update configuration" },
      { status: 500 }
    )
  }
}

// PUT - Reset to default configuration
export async function PUT() {
  try {
    currentConfig = { ...DEFAULT_CONFIG }

    return NextResponse.json({
      config: currentConfig,
      success: true,
      message: "Configuration reset to defaults",
    })

  } catch (error) {
    console.error("Config PUT error:", error)
    return NextResponse.json(
      { error: "Failed to reset configuration" },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function DELETE() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  )
}
