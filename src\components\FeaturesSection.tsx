"use client";

import React from "react";
import { motion } from "framer-motion";
// Button and icons are now used in MarketResearchCards component
import { SparklesDemo } from "@/components/ui/sparkles-demo";
import { MarketResearchCards } from "@/components/ui/market-research-cards";

const FeaturesSection = () => {
  return (
    <section id="features" className="py-24 bg-black">
      <div className="container mx-auto px-6">
        {/* Market Research Cards Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mb-24"
        >
          <MarketResearchCards />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <SparklesDemo />
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturesSection;
