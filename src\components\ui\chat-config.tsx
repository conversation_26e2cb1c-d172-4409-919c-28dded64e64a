"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "./button"
import { Input } from "./input"
import { Label } from "./label"
import { <PERSON>, CardHeader, CardContent } from "./card"
import { Badge } from "./badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "./tabs"
import { 
  <PERSON>tings, 
  <PERSON><PERSON>, 
  MessageSquare, 
  Zap,
  Save,
  RotateCcw,
  Eye,
  EyeOff
} from "lucide-react"

export interface ChatConfigData {
  systemPrompt: string
  temperature: number
  model: string
  maxMessages: number
  title: string
  subtitle: string
  placeholder: string
  position: "bottom-right" | "bottom-left" | "top-right" | "top-left"
  theme: {
    primaryColor: string
    backgroundColor: string
    textColor: string
  }
  features: {
    persistMessages: boolean
    showTypingIndicator: boolean
    showTimestamps: boolean
    allowFileUpload: boolean
    maxFileSize: number
  }
}

interface ChatConfigProps {
  config: ChatConfigData
  onConfigChange: (config: Partial<ChatConfigData>) => void
  onSave?: () => void
  onReset?: () => void
  className?: string
}

export function ChatConfig({
  config,
  onConfigChange,
  onSave,
  onReset,
  className,
}: ChatConfigProps) {
  const [showSystemPrompt, setShowSystemPrompt] = React.useState(false)

  const handleInputChange = (field: keyof ChatConfigData, value: any) => {
    onConfigChange({ [field]: value })
  }

  const handleThemeChange = (field: keyof ChatConfigData["theme"], value: string) => {
    onConfigChange({
      theme: {
        ...config.theme,
        [field]: value,
      },
    })
  }

  const handleFeatureChange = (field: keyof ChatConfigData["features"], value: any) => {
    onConfigChange({
      features: {
        ...config.features,
        [field]: value,
      },
    })
  }

  return (
    <Card className={cn("w-full max-w-4xl", className)}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          <h2 className="text-lg font-semibold">Chatbot Configuration</h2>
        </div>
        <div className="flex items-center gap-2">
          {onReset && (
            <Button variant="outline" size="sm" onClick={onReset}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          )}
          {onSave && (
            <Button size="sm" onClick={onSave}>
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general">
              <MessageSquare className="h-4 w-4 mr-2" />
              General
            </TabsTrigger>
            <TabsTrigger value="ai">
              <Zap className="h-4 w-4 mr-2" />
              AI Settings
            </TabsTrigger>
            <TabsTrigger value="appearance">
              <Palette className="h-4 w-4 mr-2" />
              Appearance
            </TabsTrigger>
            <TabsTrigger value="features">
              <Settings className="h-4 w-4 mr-2" />
              Features
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Chat Title</Label>
                <Input
                  id="title"
                  value={config.title}
                  onChange={(e) => handleInputChange("title", e.target.value)}
                  placeholder="AI Assistant"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="subtitle">Subtitle</Label>
                <Input
                  id="subtitle"
                  value={config.subtitle}
                  onChange={(e) => handleInputChange("subtitle", e.target.value)}
                  placeholder="How can I help you today?"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="placeholder">Input Placeholder</Label>
                <Input
                  id="placeholder"
                  value={config.placeholder}
                  onChange={(e) => handleInputChange("placeholder", e.target.value)}
                  placeholder="Type your message..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="position">Widget Position</Label>
                <select
                  id="position"
                  value={config.position}
                  onChange={(e) => handleInputChange("position", e.target.value)}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="bottom-right">Bottom Right</option>
                  <option value="bottom-left">Bottom Left</option>
                  <option value="top-right">Top Right</option>
                  <option value="top-left">Top Left</option>
                </select>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="ai" className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="systemPrompt">System Prompt</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSystemPrompt(!showSystemPrompt)}
                  >
                    {showSystemPrompt ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {showSystemPrompt ? (
                  <textarea
                    id="systemPrompt"
                    value={config.systemPrompt}
                    onChange={(e) => handleInputChange("systemPrompt", e.target.value)}
                    className="flex min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    placeholder="You are a helpful AI assistant..."
                  />
                ) : (
                  <div className="p-3 bg-muted rounded-md text-sm text-muted-foreground">
                    System prompt is hidden. Click the eye icon to view/edit.
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="model">AI Model</Label>
                  <select
                    id="model"
                    value={config.model}
                    onChange={(e) => handleInputChange("model", e.target.value)}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    <option value="gpt-4">GPT-4</option>
                    <option value="gpt-4-turbo-preview">GPT-4 Turbo</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="temperature">
                    Temperature: {config.temperature}
                  </Label>
                  <input
                    id="temperature"
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={config.temperature}
                    onChange={(e) => handleInputChange("temperature", parseFloat(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Focused</span>
                    <span>Creative</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxMessages">Max Messages</Label>
                  <Input
                    id="maxMessages"
                    type="number"
                    min="1"
                    max="100"
                    value={config.maxMessages}
                    onChange={(e) => handleInputChange("maxMessages", parseInt(e.target.value))}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="appearance" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="primaryColor">Primary Color</Label>
                <div className="flex items-center gap-2">
                  <input
                    id="primaryColor"
                    type="color"
                    value={config.theme.primaryColor}
                    onChange={(e) => handleThemeChange("primaryColor", e.target.value)}
                    className="w-12 h-10 rounded border border-input cursor-pointer"
                  />
                  <Input
                    value={config.theme.primaryColor}
                    onChange={(e) => handleThemeChange("primaryColor", e.target.value)}
                    placeholder="#000000"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="backgroundColor">Background Color</Label>
                <div className="flex items-center gap-2">
                  <input
                    id="backgroundColor"
                    type="color"
                    value={config.theme.backgroundColor}
                    onChange={(e) => handleThemeChange("backgroundColor", e.target.value)}
                    className="w-12 h-10 rounded border border-input cursor-pointer"
                  />
                  <Input
                    value={config.theme.backgroundColor}
                    onChange={(e) => handleThemeChange("backgroundColor", e.target.value)}
                    placeholder="#ffffff"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="textColor">Text Color</Label>
                <div className="flex items-center gap-2">
                  <input
                    id="textColor"
                    type="color"
                    value={config.theme.textColor}
                    onChange={(e) => handleThemeChange("textColor", e.target.value)}
                    className="w-12 h-10 rounded border border-input cursor-pointer"
                  />
                  <Input
                    value={config.theme.textColor}
                    onChange={(e) => handleThemeChange("textColor", e.target.value)}
                    placeholder="#000000"
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="features" className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Persist Messages</Label>
                  <p className="text-sm text-muted-foreground">
                    Save conversation history in browser storage
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={config.features.persistMessages}
                  onChange={(e) => handleFeatureChange("persistMessages", e.target.checked)}
                  className="h-4 w-4"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Show Typing Indicator</Label>
                  <p className="text-sm text-muted-foreground">
                    Display typing animation when AI is responding
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={config.features.showTypingIndicator}
                  onChange={(e) => handleFeatureChange("showTypingIndicator", e.target.checked)}
                  className="h-4 w-4"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Show Timestamps</Label>
                  <p className="text-sm text-muted-foreground">
                    Display message timestamps
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={config.features.showTimestamps}
                  onChange={(e) => handleFeatureChange("showTimestamps", e.target.checked)}
                  className="h-4 w-4"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Allow File Upload</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable file upload functionality (coming soon)
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={config.features.allowFileUpload}
                  onChange={(e) => handleFeatureChange("allowFileUpload", e.target.checked)}
                  className="h-4 w-4"
                  disabled
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
