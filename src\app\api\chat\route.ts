import { NextRequest, NextResponse } from "next/server"
import OpenAI from "openai"

// Rate limiting store (in production, use Redis or a proper database)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Configuration
const RATE_LIMIT_REQUESTS = parseInt(process.env.CHAT_RATE_LIMIT_REQUESTS || "10")
const RATE_LIMIT_WINDOW = parseInt(process.env.CHAT_RATE_LIMIT_WINDOW_MS || "60000")
const MAX_TOKENS = parseInt(process.env.CHAT_MAX_TOKENS || "1500")
const DEFAULT_MODEL = process.env.CHAT_DEFAULT_MODEL || "gpt-4"

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

interface ChatRequest {
  message: string
  conversationId?: string
  config?: {
    systemPrompt?: string
    temperature?: number
    model?: string
    maxMessages?: number
  }
}

interface ChatResponse {
  message: string
  conversationId: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
}

// Simple rate limiting function
function checkRateLimit(identifier: string): boolean {
  const now = Date.now()
  const userLimit = rateLimitStore.get(identifier)

  if (!userLimit || now > userLimit.resetTime) {
    // Reset or create new limit window
    rateLimitStore.set(identifier, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW,
    })
    return true
  }

  if (userLimit.count >= RATE_LIMIT_REQUESTS) {
    return false
  }

  userLimit.count++
  return true
}

// Get client identifier for rate limiting
function getClientIdentifier(request: NextRequest): string {
  // In production, you might want to use user ID if authenticated
  const forwarded = request.headers.get("x-forwarded-for")
  const ip = forwarded ? forwarded.split(",")[0] : request.ip || "unknown"
  return ip
}

// Validate and sanitize input
function validateInput(data: any): ChatRequest | null {
  if (!data || typeof data !== "object") {
    return null
  }

  const { message, conversationId, config } = data

  if (!message || typeof message !== "string" || message.trim().length === 0) {
    return null
  }

  if (message.length > 4000) {
    return null // Message too long
  }

  return {
    message: message.trim(),
    conversationId: conversationId && typeof conversationId === "string" ? conversationId : undefined,
    config: config && typeof config === "object" ? {
      systemPrompt: config.systemPrompt && typeof config.systemPrompt === "string" ? config.systemPrompt : undefined,
      temperature: config.temperature && typeof config.temperature === "number" && config.temperature >= 0 && config.temperature <= 2 ? config.temperature : undefined,
      model: config.model && typeof config.model === "string" ? config.model : undefined,
      maxMessages: config.maxMessages && typeof config.maxMessages === "number" && config.maxMessages > 0 ? config.maxMessages : undefined,
    } : undefined,
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        { error: "OpenAI API key not configured" },
        { status: 500 }
      )
    }

    // Rate limiting
    const clientId = getClientIdentifier(request)
    if (!checkRateLimit(clientId)) {
      return NextResponse.json(
        { error: "Rate limit exceeded. Please try again later." },
        { status: 429 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedInput = validateInput(body)

    if (!validatedInput) {
      return NextResponse.json(
        { error: "Invalid request. Message is required and must be a non-empty string." },
        { status: 400 }
      )
    }

    const { message, conversationId, config } = validatedInput

    // Prepare OpenAI request
    const systemPrompt = config?.systemPrompt || 
      "You are a helpful AI assistant. Provide clear, concise, and helpful responses to user questions. Be friendly and professional."

    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
      {
        role: "system",
        content: systemPrompt,
      },
      {
        role: "user",
        content: message,
      },
    ]

    // In a real application, you would retrieve conversation history here
    // For now, we'll just use the current message

    const completion = await openai.chat.completions.create({
      model: config?.model || DEFAULT_MODEL,
      messages,
      temperature: config?.temperature ?? 0.7,
      max_tokens: MAX_TOKENS,
      stream: false,
    })

    const assistantMessage = completion.choices[0]?.message?.content

    if (!assistantMessage) {
      throw new Error("No response from OpenAI")
    }

    // Generate or use existing conversation ID
    const responseConversationId = conversationId || `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const response: ChatResponse = {
      message: assistantMessage,
      conversationId: responseConversationId,
      usage: completion.usage ? {
        promptTokens: completion.usage.prompt_tokens,
        completionTokens: completion.usage.completion_tokens,
        totalTokens: completion.usage.total_tokens,
      } : undefined,
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error("Chat API error:", error)

    // Handle specific OpenAI errors
    if (error instanceof OpenAI.APIError) {
      if (error.status === 401) {
        return NextResponse.json(
          { error: "Invalid OpenAI API key" },
          { status: 500 }
        )
      }
      if (error.status === 429) {
        return NextResponse.json(
          { error: "OpenAI API rate limit exceeded. Please try again later." },
          { status: 429 }
        )
      }
      if (error.status === 400) {
        return NextResponse.json(
          { error: "Invalid request to OpenAI API" },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { error: "Internal server error. Please try again later." },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  )
}
