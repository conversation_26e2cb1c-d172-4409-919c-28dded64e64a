"use client";

import { BarChart3, TrendingUp, Users, Target, PieChart } from "lucide-react";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import { cn } from "@/lib/utils";

export function MarketResearchCards() {
  return (
    <div className="w-full max-w-7xl mx-auto p-6">
      <div className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
          Өгөгдлийн ухаалаг платформын үндсэн боломжууд
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Монголын зах зээлийн тухай бэлэн боловсруулсан өгөгдлөөс эхлээд AI туслах үйлчилгээ хүртэл бүх шаардлагатай хэрэгслүүдийг нэг платформд.
        </p>
      </div>
      
      <ul className="grid grid-cols-1 grid-rows-none gap-4 md:grid-cols-12 md:grid-rows-3 lg:gap-6 xl:max-h-[40rem] xl:grid-rows-2">
        <GridItem
          area="md:[grid-area:1/1/2/7] xl:[grid-area:1/1/2/5]"
          icon={<BarChart3 className="h-5 w-5" />}
          title="Ухаалаг өгөгдлийн дашбоард"
          description="Хэрэглэгчдэд ээлтэй вэб дашбоард дээр өгөгдлийн багцуудыг хайх, харах, харилцан үйлчлэх. Салбар, байршил, огноо, үнийн хүрээгээр шүүх боломжтой."
          gradient="from-blue-500 to-cyan-500"
        />
        <GridItem
          area="md:[grid-area:1/7/2/13] xl:[grid-area:2/1/3/5]"
          icon={<Users className="h-5 w-5" />}
          title="Бодит цагийн AI туслах"
          description="Манай дотоод өгөгдлийн сан дээр сургагдсан ChatGPT загвар. Хэрэглэгчид судалгаа, өгөгдөлд суурилсан асуултуудыг хэлний хэлбэрээр асууж болно."
          gradient="from-purple-500 to-pink-500"
        />
        <GridItem
          area="md:[grid-area:2/1/3/7] xl:[grid-area:1/5/3/8]"
          icon={<Target className="h-5 w-5" />}
          title="Захиалгат өгөгдлийн багц бүтээгч"
          description="Хэрэглэгчид тодорхой төрлийн өгөгдлийн багц хүсэх боломжтой. Систем дотоод өгөгдлөөс хариу өгөх эсвэл дотоод шинжээчдийн багаар захиалгат судалгаа хийнэ."
          gradient="from-green-500 to-emerald-500"
        />
        <GridItem
          area="md:[grid-area:2/7/3/13] xl:[grid-area:1/8/2/13]"
          icon={<TrendingUp className="h-5 w-5" />}
          title="Тасралтгүй шинэчлэгдэх өгөгдөл"
          description="Бүх өгөгдлийн багцууд тогтмол хугацаанд шинэчлэгддэг. Хоол, жижиглэн худалдаа, хүн ам зүй, үл хөдлөх хөрөнгө, тээвэр, нийгмийн зан төлөв гэх мэт олон салбарыг хамардаг."
          gradient="from-orange-500 to-red-500"
        />
        <GridItem
          area="md:[grid-area:3/1/4/13] xl:[grid-area:2/8/3/13]"
          icon={<PieChart className="h-5 w-5" />}
          title="Захиалгат судалгааны үйлчилгээ"
          description="Үйлчлүүлэгчид тодорхой зах зээлийн судалгаа эсвэл өгөгдлийн шинжилгээ хүсэх боломжтой. Хүрээ эсвэл нарийн төвөгтэй байдлаар төлбөр тооцогддог."
          gradient="from-indigo-500 to-purple-500"
        />
      </ul>
    </div>
  );
}

interface GridItemProps {
  area: string;
  icon: React.ReactNode;
  title: string;
  description: React.ReactNode;
  gradient?: string;
}

const GridItem = ({ area, icon, title, description, gradient = "from-blue-500 to-purple-500" }: GridItemProps) => {
  return (
    <li className={cn("min-h-[16rem] list-none", area)}>
      <div className="relative h-full rounded-[1.25rem] border-[0.75px] border-border p-2 md:rounded-[1.5rem] md:p-3">
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
          borderWidth={2}
        />
        <div className="relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl border-[0.75px] bg-background p-6 shadow-sm dark:shadow-[0px_0px_27px_0px_rgba(45,45,45,0.3)] md:p-8">
          <div className="relative flex flex-1 flex-col justify-between gap-4">
            <div className={cn(
              "w-fit rounded-lg border-[0.75px] border-border p-3 bg-gradient-to-br",
              gradient
            )}>
              <div className="text-white">
                {icon}
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="pt-0.5 text-xl leading-[1.375rem] font-semibold font-sans tracking-[-0.04em] md:text-2xl md:leading-[1.875rem] text-balance text-foreground">
                {title}
              </h3>
              <p className="font-sans text-sm leading-[1.25rem] md:text-base md:leading-[1.5rem] text-muted-foreground">
                {description}
              </p>
            </div>
          </div>
        </div>
      </div>
    </li>
  );
};
